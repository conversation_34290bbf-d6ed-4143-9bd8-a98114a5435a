.blog-card {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl 
         transition-all duration-300 overflow-hidden cursor-pointer
         border border-gray-200 dark:border-gray-700;
}

/* Variants */
.blog-card--featured {
  @apply lg:col-span-2 lg:row-span-2;
}

.blog-card--featured .blog-card__title {
  @apply text-2xl lg:text-3xl;
}

.blog-card--featured .blog-card__excerpt {
  @apply text-lg;
}

.blog-card--compact {
  @apply flex flex-row;
}

.blog-card--compact .blog-card__thumbnail {
  @apply w-32 h-32 flex-shrink-0;
}

.blog-card--compact .blog-card__content {
  @apply flex-1 p-4;
}

.blog-card--compact .blog-card__title {
  @apply text-lg;
}

/* Thumbnail */
.blog-card__thumbnail {
  @apply relative aspect-video overflow-hidden;
}

.blog-card--featured .blog-card__thumbnail {
  @apply aspect-[16/10];
}

.blog-card__thumbnail img {
  @apply w-full h-full object-cover transition-transform duration-300;
}

.blog-card:hover .blog-card__thumbnail img {
  @apply scale-105;
}

.blog-card__overlay {
  @apply absolute inset-0 bg-black/50 flex items-center justify-center 
         opacity-0 hover:opacity-100 transition-opacity duration-300;
}

.overlay-icon {
  @apply w-8 h-8 text-white;
}

.blog-card__status-overlay {
  @apply absolute top-3 left-3;
}

/* Content */
.blog-card__content {
  @apply p-6;
}

.blog-card--featured .blog-card__content {
  @apply p-8;
}

.blog-card__status {
  @apply mb-3;
}

.status-badge {
  @apply inline-block px-2 py-1 text-xs font-medium text-white rounded-full;
}

.blog-card__category {
  @apply mb-2;
}

.category-badge {
  @apply inline-block px-3 py-1 text-xs font-semibold bg-blue-100 
         dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full;
}

.blog-card__title {
  @apply mb-3;
}

.blog-card__title a {
  @apply text-xl font-bold text-gray-900 dark:text-white hover:text-blue-600 
         dark:hover:text-blue-400 transition-colors line-clamp-2;
}

.blog-card__excerpt {
  @apply text-gray-600 dark:text-gray-300 mb-4 line-clamp-3;
}

.blog-card--compact .blog-card__excerpt {
  @apply line-clamp-2 text-sm;
}

/* Meta */
.blog-card__meta {
  @apply flex flex-wrap items-center gap-4 mb-4 text-sm text-gray-500 
         dark:text-gray-400;
}

.blog-card--compact .blog-card__meta {
  @apply gap-3 text-xs;
}

.meta-item {
  @apply flex items-center gap-1;
}

.meta-icon {
  @apply w-4 h-4;
}

.blog-card--compact .meta-icon {
  @apply w-3 h-3;
}

/* Tags */
.blog-card__tags {
  @apply flex flex-wrap gap-2 mb-4;
}

.blog-card--compact .blog-card__tags {
  @apply gap-1 mb-3;
}

.blog-card__tags .tag {
  @apply flex items-center gap-1 px-2 py-1 text-xs bg-gray-100 
         dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full;
}

.blog-card--compact .tag {
  @apply text-xs px-2 py-0.5;
}

.tag-more {
  @apply px-2 py-1 text-xs bg-gray-200 dark:bg-gray-600 text-gray-600 
         dark:text-gray-400 rounded-full;
}

/* Actions */
.blog-card__actions {
  @apply flex gap-2;
}

.blog-card--compact .blog-card__actions {
  @apply mt-auto;
}

.action-btn {
  @apply flex items-center gap-1 px-3 py-2 text-sm font-medium rounded-lg 
         transition-colors;
}

.blog-card--compact .action-btn {
  @apply px-2 py-1 text-xs;
}

.action-btn--primary {
  @apply bg-blue-500 text-white hover:bg-blue-600;
}

.action-btn--secondary {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 
         hover:bg-gray-200 dark:hover:bg-gray-600;
}

.action-btn--featured {
  @apply bg-blue-500 text-white hover:bg-blue-600 px-6 py-3 text-base;
}

/* Hover effects */
.blog-card:hover {
  @apply transform -translate-y-1 shadow-2xl;
}

.blog-card--featured:hover {
  @apply transform -translate-y-2;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .blog-card--featured {
    @apply col-span-1 row-span-1;
  }
  
  .blog-card--featured .blog-card__title {
    @apply text-xl;
  }
  
  .blog-card--featured .blog-card__excerpt {
    @apply text-base;
  }
  
  .blog-card--featured .blog-card__content {
    @apply p-6;
  }
  
  .blog-card--compact {
    @apply flex-col;
  }
  
  .blog-card--compact .blog-card__thumbnail {
    @apply w-full h-48;
  }
}

/* Utility classes */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Animation variants */
.blog-card--featured {
  animation: fadeInScale 0.6s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Focus states for accessibility */
.blog-card:focus-within {
  @apply ring-2 ring-blue-500 ring-offset-2;
}

.action-btn:focus {
  @apply ring-2 ring-blue-500 ring-offset-2;
}

/* Dark mode specific adjustments */
@media (prefers-color-scheme: dark) {
  .blog-card__overlay {
    @apply bg-black/70;
  }
  
  .category-badge {
    @apply bg-blue-900/50 text-blue-300;
  }
}

.blog-list {
  @apply w-full max-w-7xl mx-auto px-4 py-8;
}

/* Header */
.blog-list__header {
  @apply flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8;
}

.blog-list__title {
  @apply flex flex-col gap-1;
}

.blog-list__title h2 {
  @apply text-3xl font-bold text-gray-900 dark:text-white;
}

.blog-list__count {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.blog-list__controls {
  @apply flex flex-col sm:flex-row items-stretch sm:items-center gap-3;
}

/* Search */
.blog-list__search {
  @apply relative flex items-center;
}

.search-icon {
  @apply absolute left-3 w-4 h-4 text-gray-400;
}

.search-input {
  @apply pl-10 pr-10 py-2 w-64 border border-gray-300 rounded-lg 
         focus:ring-2 focus:ring-blue-500 focus:border-transparent
         dark:bg-gray-800 dark:border-gray-600 dark:text-white;
}

.search-clear {
  @apply absolute right-3 w-4 h-4 text-gray-400 hover:text-gray-600 
         transition-colors cursor-pointer;
}

/* View toggle */
.blog-list__view-toggle {
  @apply flex border border-gray-300 rounded-lg overflow-hidden dark:border-gray-600;
}

.view-toggle-btn {
  @apply px-3 py-2 bg-white hover:bg-gray-50 border-r border-gray-300 
         last:border-r-0 transition-colors dark:bg-gray-800 dark:hover:bg-gray-700
         dark:border-gray-600;
}

.view-toggle-btn.active {
  @apply bg-blue-500 text-white hover:bg-blue-600;
}

/* Filter toggle */
.filter-toggle {
  @apply flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 
         rounded-lg hover:bg-gray-50 transition-colors dark:bg-gray-800 
         dark:border-gray-600 dark:hover:bg-gray-700;
}

.filter-toggle.active {
  @apply bg-blue-500 text-white border-blue-500 hover:bg-blue-600;
}

/* Filter panel */
.blog-list__filter-panel {
  @apply mb-6 overflow-hidden;
}

.filter-panel__content {
  @apply bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 
         dark:border-gray-700;
}

.filter-group {
  @apply mb-4 last:mb-0;
}

.filter-group label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2;
}

.filter-group select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg 
         focus:ring-2 focus:ring-blue-500 focus:border-transparent
         dark:bg-gray-700 dark:border-gray-600 dark:text-white;
}

.tag-filters {
  @apply flex flex-wrap gap-2;
}

.tag-filter {
  @apply flex items-center gap-1 px-3 py-1 text-sm bg-white border 
         border-gray-300 rounded-full hover:bg-gray-50 transition-colors
         dark:bg-gray-700 dark:border-gray-600 dark:hover:bg-gray-600;
}

.tag-filter.active {
  @apply bg-blue-500 text-white border-blue-500 hover:bg-blue-600;
}

.clear-filters-btn {
  @apply mt-4 px-4 py-2 text-sm text-red-600 hover:text-red-700 
         hover:bg-red-50 rounded-lg transition-colors dark:text-red-400 
         dark:hover:bg-red-900/20;
}

/* Active filters */
.blog-list__active-filters {
  @apply flex flex-wrap items-center gap-2 mb-6 p-4 bg-blue-50 
         dark:bg-blue-900/20 rounded-lg border border-blue-200 
         dark:border-blue-800;
}

.active-filter {
  @apply flex items-center gap-1 px-3 py-1 text-sm bg-blue-100 
         dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded-full;
}

.active-filter button {
  @apply ml-1 w-4 h-4 text-blue-600 dark:text-blue-300 hover:text-blue-800 
         dark:hover:text-blue-100 transition-colors;
}

/* Content grid/list */
.blog-list__content {
  @apply mb-8;
}

.blog-list__content--grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.blog-list__content--list {
  @apply flex flex-col gap-4;
}

/* Blog cards */
.blog-card {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl 
         transition-all duration-300 overflow-hidden cursor-pointer
         border border-gray-200 dark:border-gray-700;
}

.blog-card--list {
  @apply flex flex-row;
}

.blog-card--list .blog-card__thumbnail {
  @apply w-48 flex-shrink-0;
}

.blog-card--list .blog-card__content {
  @apply flex-1;
}

/* Thumbnail */
.blog-card__thumbnail {
  @apply relative aspect-video overflow-hidden;
}

.blog-card__thumbnail img {
  @apply w-full h-full object-cover transition-transform duration-300;
}

.blog-card:hover .blog-card__thumbnail img {
  @apply scale-105;
}

.blog-card__overlay {
  @apply absolute inset-0 bg-black/50 flex items-center justify-center 
         opacity-0 hover:opacity-100 transition-opacity duration-300;
}

.overlay-icon {
  @apply w-8 h-8 text-white;
}

/* Content */
.blog-card__content {
  @apply p-6;
}

.blog-card__status {
  @apply mb-3;
}

.status-badge {
  @apply inline-block px-2 py-1 text-xs font-medium text-white rounded-full;
}

.blog-card__title {
  @apply mb-3;
}

.blog-card__title a {
  @apply text-xl font-bold text-gray-900 dark:text-white hover:text-blue-600 
         dark:hover:text-blue-400 transition-colors line-clamp-2;
}

.blog-card__excerpt {
  @apply text-gray-600 dark:text-gray-300 mb-4 line-clamp-3;
}

/* Meta */
.blog-card__meta {
  @apply flex flex-wrap items-center gap-4 mb-4 text-sm text-gray-500 
         dark:text-gray-400;
}

.meta-item {
  @apply flex items-center gap-1;
}

.meta-icon {
  @apply w-4 h-4;
}

/* Tags */
.blog-card__tags {
  @apply flex flex-wrap gap-2 mb-4;
}

.blog-card__tags .tag {
  @apply flex items-center gap-1 px-2 py-1 text-xs bg-gray-100 
         dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full;
}

.tag-more {
  @apply px-2 py-1 text-xs bg-gray-200 dark:bg-gray-600 text-gray-600 
         dark:text-gray-400 rounded-full;
}

/* Actions */
.blog-card__actions {
  @apply flex gap-2;
}

.action-btn {
  @apply flex items-center gap-1 px-3 py-2 text-sm font-medium rounded-lg 
         transition-colors;
}

.action-btn--primary {
  @apply bg-blue-500 text-white hover:bg-blue-600;
}

.action-btn--secondary {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 
         hover:bg-gray-200 dark:hover:bg-gray-600;
}

/* Pagination */
.blog-list__pagination {
  @apply flex items-center justify-between;
}

.pagination-btn {
  @apply flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 
         border border-gray-300 dark:border-gray-600 rounded-lg 
         hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors
         disabled:opacity-50 disabled:cursor-not-allowed;
}

.pagination-info {
  @apply text-center;
}

.pagination-total {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

/* Loading and error states */
.blog-list__loading,
.blog-list__error,
.blog-list__empty {
  @apply flex flex-col items-center justify-center py-12 text-gray-500 
         dark:text-gray-400;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full mb-4;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .blog-list__controls {
    @apply flex-col items-stretch;
  }
  
  .search-input {
    @apply w-full;
  }
  
  .blog-card--list {
    @apply flex-col;
  }
  
  .blog-card--list .blog-card__thumbnail {
    @apply w-full;
  }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.blog-card {
  animation: fadeIn 0.4s ease-out;
}

/* Utility classes */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.blog-section {
  @apply relative py-20 bg-gradient-to-br from-gray-50 to-white 
         dark:from-gray-900 dark:to-gray-800 overflow-hidden;
}

.blog-section__container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10;
}

/* Header */
.blog-section__header {
  @apply text-center mb-16;
}

.section-badge {
  @apply inline-flex items-center gap-2 px-4 py-2 bg-blue-100 
         dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 
         rounded-full text-sm font-medium mb-6;
}

.badge-icon {
  @apply w-4 h-4;
}

.section-title {
  @apply text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  @apply text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto;
}

/* Content */
.blog-section__content {
  @apply mb-16;
}

.blog-section__featured {
  @apply mb-12;
}

.blog-section__grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8;
}

/* Call to Action */
.blog-section__cta {
  @apply flex flex-col sm:flex-row items-center justify-between 
         p-8 bg-white dark:bg-gray-800 rounded-2xl shadow-lg 
         border border-gray-200 dark:border-gray-700;
}

.cta-content {
  @apply flex items-center gap-4 mb-6 sm:mb-0;
}

.cta-icon {
  @apply w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl 
         flex items-center justify-center text-blue-600 dark:text-blue-400;
}

.cta-icon svg {
  @apply w-6 h-6;
}

.cta-text h3 {
  @apply text-xl font-bold text-gray-900 dark:text-white mb-1;
}

.cta-text p {
  @apply text-gray-600 dark:text-gray-300;
}

.cta-button {
  @apply inline-flex items-center gap-2 px-6 py-3 bg-blue-600 
         hover:bg-blue-700 text-white font-medium rounded-xl 
         transition-all duration-300 hover:scale-105 hover:shadow-lg;
}

/* Loading and Error States */
.blog-section__loading,
.blog-section__error {
  @apply flex flex-col items-center justify-center py-20 text-gray-500 
         dark:text-gray-400;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full mb-4;
}

/* Empty State */
.blog-section__empty {
  @apply text-center py-20;
}

.empty-icon {
  @apply w-16 h-16 text-gray-400 dark:text-gray-500 mx-auto mb-6;
}

.blog-section__empty h3 {
  @apply text-2xl font-bold text-gray-900 dark:text-white mb-2;
}

.blog-section__empty p {
  @apply text-gray-600 dark:text-gray-300 mb-8;
}

/* Background Decoration */
.blog-section__decoration {
  @apply absolute inset-0 pointer-events-none;
}

.decoration-circle {
  @apply absolute rounded-full opacity-10;
}

.decoration-circle--1 {
  @apply w-96 h-96 bg-blue-500 -top-48 -right-48;
  animation: float 6s ease-in-out infinite;
}

.decoration-circle--2 {
  @apply w-64 h-64 bg-purple-500 top-1/2 -left-32;
  animation: float 8s ease-in-out infinite reverse;
}

.decoration-circle--3 {
  @apply w-32 h-32 bg-pink-500 bottom-20 right-20;
  animation: float 4s ease-in-out infinite;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .blog-section {
    @apply py-16;
  }
  
  .blog-section__header {
    @apply mb-12;
  }
  
  .section-title {
    @apply text-3xl;
  }
  
  .section-subtitle {
    @apply text-lg;
  }
  
  .blog-section__featured {
    @apply mb-8;
  }
  
  .blog-section__grid {
    @apply gap-6;
  }
  
  .blog-section__cta {
    @apply p-6 text-center;
  }
  
  .cta-content {
    @apply flex-col text-center;
  }
}

/* Dark mode specific styles */
@media (prefers-color-scheme: dark) {
  .blog-section {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  }
  
  .section-title {
    background: linear-gradient(135deg, #90cdf4 0%, #a78bfa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* Hover effects */
.blog-section__featured:hover {
  transform: translateY(-5px);
  transition: transform 0.3s ease;
}

.blog-section__grid > div:hover {
  transform: translateY(-3px);
  transition: transform 0.3s ease;
}

/* Focus states for accessibility */
.cta-button:focus {
  @apply ring-2 ring-blue-500 ring-offset-2 ring-offset-white 
         dark:ring-offset-gray-800;
}

/* Print styles */
@media print {
  .blog-section__decoration {
    display: none;
  }
  
  .blog-section {
    @apply bg-white text-black;
  }
}

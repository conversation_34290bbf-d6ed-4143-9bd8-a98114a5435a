import { useState, useEffect, useCallback, useMemo } from 'react';
import { BlogListItem, BlogFilter, BlogPagination, BlogListResponse } from '../types/common';
import { BlogLocalStorage } from '../utils/blogStorage';

export interface UseBlogListOptions {
  initialFilter?: BlogFilter;
  pageSize?: number;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface UseBlogListReturn {
  // Data
  blogs: BlogListItem[];
  filteredBlogs: BlogListItem[];
  pagination: BlogPagination;
  
  // State
  isLoading: boolean;
  error: string | null;
  
  // Filters
  filter: BlogFilter;
  setFilter: (filter: BlogFilter) => void;
  updateFilter: (partialFilter: Partial<BlogFilter>) => void;
  clearFilter: () => void;
  
  // Pagination
  currentPage: number;
  setCurrentPage: (page: number) => void;
  nextPage: () => void;
  previousPage: () => void;
  
  // Actions
  refresh: () => void;
  searchBlogs: (query: string) => void;
  filterByTag: (tag: string) => void;
  filterByCategory: (category: string) => void;
  filterByStatus: (status: 'draft' | 'published' | 'archived') => void;
  
  // Utilities
  getTotalCount: () => number;
  getUniqueCategories: () => string[];
  getUniqueTags: () => string[];
  getRecentBlogs: (count?: number) => BlogListItem[];
  getBlogsByTag: (tag: string) => BlogListItem[];
  getBlogsByCategory: (category: string) => BlogListItem[];
}

export const useBlogList = (options: UseBlogListOptions = {}): UseBlogListReturn => {
  const {
    initialFilter = {},
    pageSize = 10,
    autoRefresh = false,
    refreshInterval = 30000
  } = options;

  const [blogs, setBlogs] = useState<BlogListItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<BlogFilter>(initialFilter);
  const [currentPage, setCurrentPage] = useState(1);

  // Load blogs from storage
  const loadBlogs = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const blogMetadata = BlogLocalStorage.getMetadataIndex();
      setBlogs(blogMetadata);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load blogs';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Filter blogs based on current filter
  const filteredBlogs = useMemo(() => {
    let filtered = [...blogs];

    // Search filter
    if (filter.search) {
      const searchLower = filter.search.toLowerCase();
      filtered = filtered.filter(blog =>
        blog.title.toLowerCase().includes(searchLower) ||
        blog.excerpt.toLowerCase().includes(searchLower) ||
        blog.author.toLowerCase().includes(searchLower) ||
        blog.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // Tags filter
    if (filter.tags && filter.tags.length > 0) {
      filtered = filtered.filter(blog =>
        filter.tags!.some(tag => blog.tags.includes(tag))
      );
    }

    // Category filter
    if (filter.category) {
      filtered = filtered.filter(blog => blog.category === filter.category);
    }

    // Status filter
    if (filter.status) {
      filtered = filtered.filter(blog => blog.status === filter.status);
    }

    // Author filter
    if (filter.author) {
      filtered = filtered.filter(blog => blog.author === filter.author);
    }

    // Date range filter
    if (filter.dateRange) {
      filtered = filtered.filter(blog => {
        const blogDate = new Date(blog.createdAt);
        return blogDate >= filter.dateRange!.start && blogDate <= filter.dateRange!.end;
      });
    }

    // Sort by updatedAt descending (most recent first)
    filtered.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());

    return filtered;
  }, [blogs, filter]);

  // Pagination
  const pagination = useMemo((): BlogPagination => {
    const total = filteredBlogs.length;
    const totalPages = Math.ceil(total / pageSize);
    
    return {
      page: currentPage,
      limit: pageSize,
      total,
      totalPages
    };
  }, [filteredBlogs.length, currentPage, pageSize]);

  // Get paginated blogs
  const paginatedBlogs = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredBlogs.slice(startIndex, endIndex);
  }, [filteredBlogs, currentPage, pageSize]);

  // Filter management
  const updateFilter = useCallback((partialFilter: Partial<BlogFilter>) => {
    setFilter(prev => ({ ...prev, ...partialFilter }));
    setCurrentPage(1); // Reset to first page when filter changes
  }, []);

  const clearFilter = useCallback(() => {
    setFilter({});
    setCurrentPage(1);
  }, []);

  // Pagination actions
  const nextPage = useCallback(() => {
    if (currentPage < pagination.totalPages) {
      setCurrentPage(prev => prev + 1);
    }
  }, [currentPage, pagination.totalPages]);

  const previousPage = useCallback(() => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1);
    }
  }, [currentPage]);

  // Search and filter actions
  const searchBlogs = useCallback((query: string) => {
    updateFilter({ search: query });
  }, [updateFilter]);

  const filterByTag = useCallback((tag: string) => {
    updateFilter({ tags: [tag] });
  }, [updateFilter]);

  const filterByCategory = useCallback((category: string) => {
    updateFilter({ category });
  }, [updateFilter]);

  const filterByStatus = useCallback((status: 'draft' | 'published' | 'archived') => {
    updateFilter({ status });
  }, [updateFilter]);

  // Utility functions
  const getTotalCount = useCallback(() => blogs.length, [blogs.length]);

  const getUniqueCategories = useCallback(() => {
    const categories = blogs
      .map(blog => blog.category)
      .filter((category): category is string => Boolean(category));
    return [...new Set(categories)];
  }, [blogs]);

  const getUniqueTags = useCallback(() => {
    const allTags = blogs.flatMap(blog => blog.tags);
    return [...new Set(allTags)];
  }, [blogs]);

  const getRecentBlogs = useCallback((count: number = 5) => {
    return blogs
      .filter(blog => blog.status === 'published')
      .sort((a, b) => new Date(b.publishedAt || b.updatedAt).getTime() - new Date(a.publishedAt || a.updatedAt).getTime())
      .slice(0, count);
  }, [blogs]);

  const getBlogsByTag = useCallback((tag: string) => {
    return blogs.filter(blog => blog.tags.includes(tag));
  }, [blogs]);

  const getBlogsByCategory = useCallback((category: string) => {
    return blogs.filter(blog => blog.category === category);
  }, [blogs]);

  const refresh = useCallback(() => {
    loadBlogs();
  }, [loadBlogs]);

  // Initial load
  useEffect(() => {
    loadBlogs();
  }, [loadBlogs]);

  // Auto refresh
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(loadBlogs, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, loadBlogs]);

  return {
    // Data
    blogs: paginatedBlogs,
    filteredBlogs,
    pagination,
    
    // State
    isLoading,
    error,
    
    // Filters
    filter,
    setFilter,
    updateFilter,
    clearFilter,
    
    // Pagination
    currentPage,
    setCurrentPage,
    nextPage,
    previousPage,
    
    // Actions
    refresh,
    searchBlogs,
    filterByTag,
    filterByCategory,
    filterByStatus,
    
    // Utilities
    getTotalCount,
    getUniqueCategories,
    getUniqueTags,
    getRecentBlogs,
    getBlogsByTag,
    getBlogsByCategory
  };
};

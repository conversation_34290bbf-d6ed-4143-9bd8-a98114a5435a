.blog-editor {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1600 900'%3E%3Cpolygon fill='%23667eea' points='957 450 539 900 1396 900'/%3E%3Cpolygon fill='%236366f1' points='957 450 872.9 900 1396 900'/%3E%3Cpolygon fill='%235b21b6' points='-60 900 398 662 816 900'/%3E%3Cpolygon fill='%237c3aed' points='337 900 398 662 816 900'/%3E%3Cpolygon fill='%238b5cf6' points='1203 546 1552 900 876 900'/%3E%3Cpolygon fill='%23a855f7' points='1203 546 1552 900 1162 900'/%3E%3Cpolygon fill='%23c084fc' points='641 695 886 900 367 900'/%3E%3Cpolygon fill='%23d8b4fe' points='587 900 641 695 886 900'/%3E%3Cpolygon fill='%23e9d5ff' points='1710 900 1401 632 1096 900'/%3E%3Cpolygon fill='%23f3e8ff' points='1710 900 1401 632 1365 900'/%3E%3Cpolygon fill='%23faf5ff' points='1210 900 971 687 725 900'/%3E%3Cpolygon fill='%23f8fafc' points='943 900 1210 900 971 687'/%3E%3C/svg%3E"),
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  background-size: cover, 800px 800px, 600px 600px, 400px 400px;
  background-position: center, 0% 0%, 100% 100%, 50% 50%;
  background-attachment: fixed;
  padding: 6rem 2rem 2rem;
  display: grid;
  grid-template-columns: 1fr 320px;
  grid-template-rows: auto auto 1fr;
  gap: 2rem;
  grid-template-areas:
    "header sidebar"
    "tags sidebar"
    "content content";
  transition: all var(--transition-normal) var(--ease-custom);
  position: relative;
  overflow-x: hidden;
}

.blog-editor::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
      45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.02) 50%,
      transparent 70%
    ),
    linear-gradient(-45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

.blog-editor > * {
  position: relative;
  z-index: 1;
}

/* Header Section */
.blog-editor__header {
  grid-area: header;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--border-radius-xl);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  min-height: 120px;
}

.theme-dark .blog-editor__header {
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.blog-editor__title-section {
  flex: 1;
  min-width: 0; /* Allow flex item to shrink below content size */
  margin-right: 1rem;
}

.blog-editor__title-input {
  width: 100%;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  font-family: var(--font-family-heading);
  color: var(--color-heading);
  background: transparent;
  border: none;
  outline: none;
  padding: 0.5rem 0;
  border-bottom: 2px solid transparent;
  transition: border-color var(--transition-normal) var(--ease-custom);
}

.blog-editor__title-input:focus {
  border-bottom-color: var(--color-primary);
}

.blog-editor__title-input::placeholder {
  color: var(--color-input-placeholder);
}

.blog-editor__meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.5rem;
  flex-wrap: wrap;
}

.blog-editor__created-at,
.blog-editor__stats,
.blog-editor__auto-save,
.blog-editor__last-saved {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: var(--font-size-sm);
}

.blog-editor__created-at {
  color: var(--color-text);
}

.blog-editor__stats {
  color: var(--color-gray-500);
}

.blog-editor__auto-save {
  color: var(--color-warning-600);
  font-weight: var(--font-weight-medium);
}

.blog-editor__last-saved {
  color: var(--color-success-600);
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Action Buttons */
.blog-editor__actions {
  display: flex;
  gap: 0.75rem;
  align-items: flex-start;
  flex-wrap: wrap;
  justify-content: flex-end;
  max-height: 80px; /* Limit height to 2 rows */
  overflow-y: auto; /* Allow scrolling if needed */
  padding: 0.25rem;
  margin: -0.25rem; /* Compensate for padding */
}

/* Custom scrollbar for actions */
.blog-editor__actions::-webkit-scrollbar {
  width: 4px;
}

.blog-editor__actions::-webkit-scrollbar-track {
  background: transparent;
}

.blog-editor__actions::-webkit-scrollbar-thumb {
  background: rgba(var(--color-primary-rgb), 0.3);
  border-radius: 2px;
}

.blog-editor__actions::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--color-primary-rgb), 0.5);
}

.blog-editor__toggle,
.blog-editor__save,
.blog-editor__export,
.blog-editor__fullscreen,
.blog-editor__copy,
.blog-editor__import-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1rem;
  border: none;
  font-size: var(--font-size-sm);
  white-space: nowrap;
  border-radius: var(--border-radius-lg);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-normal) var(--ease-custom);
  font-size: var(--font-size-sm);
  text-decoration: none;
}

.blog-editor__toggle,
.blog-editor__fullscreen,
.blog-editor__copy {
  background: var(--color-gray-100);
  color: var(--color-text);
}

.blog-editor__toggle:hover {
  background: var(--color-gray-200);
  transform: translateY(-2px);
}

.blog-editor__toggle.active {
  background: var(--color-primary);
  color: var(--color-white);
}

.blog-editor__save {
  background: var(--color-primary);
  color: var(--color-white);
}

.blog-editor__save:hover {
  background: var(--color-button-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.blog-editor__export,
.blog-editor__import-btn {
  background: var(--color-secondary);
  color: var(--color-white);
}

.blog-editor__export:hover,
.blog-editor__import-btn:hover {
  background: var(--color-secondary-700);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.blog-editor__copy,
.blog-editor__fullscreen {
  background: linear-gradient(135deg, var(--color-info-500), var(--color-info-600));
  color: var(--color-white);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.blog-editor__copy:hover,
.blog-editor__fullscreen:hover {
  background: linear-gradient(135deg, var(--color-info-600), var(--color-info-700));
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* Fullscreen mode */
.blog-editor.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  padding: 1rem;
  background: var(--color-background);
}

.blog-editor.fullscreen .blog-editor__content {
  height: calc(100vh - 200px);
}

.blog-editor.fullscreen .blog-editor__sidebar {
  display: none;
}

/* Tags Section */
.blog-editor__tags {
  grid-area: tags;
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--border-radius-xl);
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
}

.theme-dark .blog-editor__tags {
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.blog-editor__tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.blog-editor__tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, var(--color-primary-100), var(--color-primary-50));
  color: var(--color-primary-700);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border: 1px solid var(--color-primary-200);
  transition: all var(--transition-normal) var(--ease-custom);
}

.blog-editor__tag:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.blog-editor__tag .icon {
  font-size: 0.75rem;
}

.blog-editor__tag-remove {
  background: none;
  border: none;
  color: var(--color-primary-600);
  cursor: pointer;
  font-size: 1.2rem;
  line-height: 1;
  margin-left: 0.25rem;
  padding: 0;
  width: 1.2rem;
  height: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color var(--transition-fast) var(--ease-custom);
}

.blog-editor__tag-remove:hover {
  background: var(--color-primary-200);
}

.blog-editor__tag-input {
  display: flex;
  gap: 0.5rem;
}

.blog-editor__tag-input input {
  flex: 1;
  padding: 0.5rem 1rem;
  border: 1px solid var(--color-input-border);
  border-radius: var(--border-radius-lg);
  background: var(--color-input-bg);
  color: var(--color-input-text);
  font-size: var(--font-size-sm);
  transition: all var(--transition-normal) var(--ease-custom);
}

.blog-editor__tag-input input:focus {
  outline: none;
  border-color: var(--color-input-focus-border);
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
}

.blog-editor__tag-input button {
  padding: 0.5rem 1rem;
  background: var(--color-primary);
  color: var(--color-white);
  border: none;
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-normal) var(--ease-custom);
}

.blog-editor__tag-input button:hover {
  background: var(--color-button-hover);
  transform: translateY(-1px);
}

/* Content Area */
.blog-editor__content {
  grid-area: content;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  height: 100%;
  min-height: 600px;
}

.blog-editor__editor-panel,
.blog-editor__preview-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--border-radius-xl);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  /* overflow: hidden; */
  backdrop-filter: blur(20px);
}

.theme-dark .blog-editor__editor-panel,
.theme-dark .blog-editor__preview-panel {
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Toolbar */
.blog-editor__toolbar {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, var(--color-gray-50), var(--color-gray-100));
  border-bottom: 1px solid var(--color-border);
  flex-wrap: wrap;
}

.theme-dark .blog-editor__toolbar {
  background: linear-gradient(135deg, var(--color-gray-800), var(--color-gray-700));
}

.blog-editor__toolbar-group {
  display: flex;
  gap: 0.5rem;
}

.blog-editor__toolbar-separator {
  width: 1px;
  height: 2rem;
  background: var(--color-border);
}

.blog-editor__toolbar button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: var(--color-white);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all var(--transition-normal) var(--ease-custom);
  color: var(--color-text);
  position: relative;
}

.blog-editor__toolbar button:hover {
  background: var(--color-primary);
  color: var(--color-white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.blog-editor__toolbar button:active {
  transform: translateY(0);
}

/* Textarea */
.blog-editor__textarea {
  flex: 1;
  padding: 2rem;
  border: none;
  outline: none;
  background: var(--color-input-bg);
  color: var(--color-input-text);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: 1.6;
  resize: none;
  transition: background-color var(--transition-normal) var(--ease-custom);
}

.blog-editor__textarea::placeholder {
  color: var(--color-input-placeholder);
}

.blog-editor__textarea:focus {
  background: var(--color-white);
}

.theme-dark .blog-editor__textarea:focus {
  background: var(--color-gray-800);
}

/* Preview Panel */
.blog-editor__preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, var(--color-gray-50), var(--color-gray-100));
  border-bottom: 1px solid var(--color-border);
}

.theme-dark .blog-editor__preview-header {
  background: linear-gradient(135deg, var(--color-gray-800), var(--color-gray-700));
}

.blog-editor__preview-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-heading);
}

.blog-editor__theme-toggle {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--border-radius-md);
  transition: background-color var(--transition-normal) var(--ease-custom);
}

.blog-editor__theme-toggle:hover {
  background: var(--color-gray-200);
}

.theme-dark .blog-editor__theme-toggle:hover {
  background: var(--color-gray-600);
}

.blog-editor__preview-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: var(--color-input-bg);
  line-height: 1.7;
}

/* Markdown Styling in Preview */
.blog-editor__preview-content h1,
.blog-editor__preview-content h2,
.blog-editor__preview-content h3,
.blog-editor__preview-content h4,
.blog-editor__preview-content h5,
.blog-editor__preview-content h6 {
  color: var(--color-heading);
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-family: var(--font-family-heading);
}

.blog-editor__preview-content h1:first-child,
.blog-editor__preview-content h2:first-child,
.blog-editor__preview-content h3:first-child {
  margin-top: 0;
}

.blog-editor__preview-content h1 {
  font-size: var(--font-size-3xl);
  border-bottom: 2px solid var(--color-border);
  padding-bottom: 0.5rem;
}

.blog-editor__preview-content h2 {
  font-size: var(--font-size-2xl);
}

.blog-editor__preview-content h3 {
  font-size: var(--font-size-xl);
}

.blog-editor__preview-content p {
  color: var(--color-text);
  margin-bottom: 1rem;
}

.blog-editor__preview-content strong {
  font-weight: var(--font-weight-bold);
  color: var(--color-heading);
}

.blog-editor__preview-content em {
  font-style: italic;
  color: var(--color-primary);
}

.blog-editor__preview-content code {
  background: var(--color-gray-100);
  padding: 0.2rem 0.4rem;
  border-radius: var(--border-radius-sm);
  font-family: var(--font-family-mono);
  font-size: 0.9em;
  color: var(--color-primary-700);
}

.theme-dark .blog-editor__preview-content code {
  background: var(--color-gray-700);
  color: var(--color-primary-300);
}

.blog-editor__preview-content pre {
  margin: 1.5rem 0;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.blog-editor__preview-content blockquote {
  border-left: 4px solid var(--color-primary);
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  background: var(--color-primary-50);
  color: var(--color-gray-700);
  font-style: italic;
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

.theme-dark .blog-editor__preview-content blockquote {
  background: var(--color-primary-900);
  color: var(--color-gray-300);
}

.blog-editor__preview-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  box-shadow: var(--shadow-sm);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

.blog-editor__preview-content th,
.blog-editor__preview-content td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid var(--color-border);
}

.blog-editor__preview-content th {
  background: var(--color-primary-50);
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary-700);
}

.theme-dark .blog-editor__preview-content th {
  background: var(--color-primary-900);
  color: var(--color-primary-300);
}

.blog-editor__preview-content ul,
.blog-editor__preview-content ol {
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.blog-editor__preview-content li {
  margin-bottom: 0.5rem;
}

.blog-editor__preview-content hr {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--color-border), transparent);
  margin: 2rem 0;
}

.blog-editor__preview-content a {
  color: var(--color-primary);
  text-decoration: underline;
  transition: color var(--transition-normal) var(--ease-custom);
}

.blog-editor__preview-content a:hover {
  color: var(--color-primary-700);
}

/* Sidebar */
.blog-editor__sidebar {
  grid-area: sidebar;
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--border-radius-xl);
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  /* height: fit-content; */
  max-height: 331px;
  overflow: auto;
  backdrop-filter: blur(20px);
}

.theme-dark .blog-editor__sidebar {
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.blog-editor__modification-history h3 {
  margin-bottom: 1.5rem;
  color: var(--color-heading);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.blog-editor__timeline {
  position: relative;
}

.blog-editor__timeline::before {
  content: "";
  position: absolute;
  left: 0.5rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, var(--color-primary), var(--color-primary-300));
}

.blog-editor__timeline-entry {
  position: relative;
  padding-left: 2rem;
  margin-bottom: 1.5rem;
}

.blog-editor__timeline-dot {
  position: absolute;
  left: 0;
  top: 0.25rem;
  width: 1rem;
  height: 1rem;
  background: var(--color-primary);
  border-radius: 50%;
  border: 2px solid var(--color-background);
  box-shadow: var(--shadow-sm);
}

.blog-editor__timeline-content {
  background: var(--color-gray-50);
  padding: 1rem;
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--color-border);
  transition: all var(--transition-normal) var(--ease-custom);
}

.blog-editor__timeline-content:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.theme-dark .blog-editor__timeline-content {
  background: var(--color-gray-800);
}

.blog-editor__timeline-description {
  margin: 0 0 0.5rem 0;
  font-weight: var(--font-weight-medium);
  color: var(--color-heading);
}

.blog-editor__timeline-meta {
  margin: 0;
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
}

.blog-editor__no-history {
  text-align: center;
  color: var(--color-gray-500);
  font-style: italic;
  padding: 2rem 0;
  line-height: 1.6;
}

/* Custom Scrollbar */
.blog-editor__preview-content::-webkit-scrollbar {
  width: 8px;
}

.blog-editor__preview-content::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: var(--border-radius-sm);
}

.blog-editor__preview-content::-webkit-scrollbar-thumb {
  background: var(--color-gray-400);
  border-radius: var(--border-radius-sm);
}

.blog-editor__preview-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-500);
}

.theme-dark .blog-editor__preview-content::-webkit-scrollbar-track {
  background: var(--color-gray-700);
}

.theme-dark .blog-editor__preview-content::-webkit-scrollbar-thumb {
  background: var(--color-gray-500);
}

.theme-dark .blog-editor__preview-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

/* Responsive Design */
@media (max-width: 1400px) {
  .blog-editor {
    grid-template-columns: 1fr 280px;
  }
}

@media (max-width: 1200px) {
  .blog-editor {
    grid-template-columns: 1fr;
    grid-template-areas:
      "header"
      "sidebar"
      "tags"
      "content";
  }

  .blog-editor__content {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 1fr;
    min-height: 800px;
  }
}

/* Medium screens - optimize button layout */
@media (max-width: 1024px) {
  .blog-editor__actions {
    max-width: 350px;
    gap: 0.5rem;
  }
  
  .blog-editor__toggle,
  .blog-editor__save,
  .blog-editor__export,
  .blog-editor__fullscreen,
  .blog-editor__copy,
  .blog-editor__import-btn {
    padding: 0.5rem 0.75rem;
    font-size: var(--font-size-xs);
  }
}

@media (max-width: 768px) {
  .blog-editor {
    padding: 1rem;
    gap: 1rem;
  }

  .blog-editor__header {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
    min-height: auto;
  }
  
  .blog-editor__title-section {
    margin-right: 0;
  }

  .blog-editor__actions {
    justify-content: center;
    max-width: none;
    gap: 0.5rem;
  }
  
  .blog-editor__toggle,
  .blog-editor__save,
  .blog-editor__export,
  .blog-editor__fullscreen,
  .blog-editor__copy,
  .blog-editor__import-btn {
    flex: 1;
    min-width: 0;
    justify-content: center;
    padding: 0.6rem 0.5rem;
  }

  .blog-editor__content {
    grid-template-rows: auto auto;
    gap: 1rem;
    min-height: 600px;
  }

  .blog-editor__toolbar {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .blog-editor__textarea,
  .blog-editor__preview-content {
    padding: 1rem;
  }

  .blog-editor__title-input {
    font-size: var(--font-size-2xl);
  }
}

/* Focus and Accessibility */
.blog-editor__textarea:focus,
.blog-editor__tag-input input:focus {
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
}

.blog-editor__toolbar button:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Enhanced animations */
.blog-editor__textarea {
  transition: all var(--transition-normal) var(--ease-custom);
}

.blog-editor__textarea:focus {
  transform: scale(1.001);
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
}

.blog-editor__preview-content {
  transition: all var(--transition-normal) var(--ease-custom);
}

.blog-editor__preview-content:hover {
  transform: translateY(-1px);
}

/* Toolbar button animations */
.blog-editor__toolbar button {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal) var(--ease-custom);
}

.blog-editor__toolbar button::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(var(--color-primary-rgb), 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.blog-editor__toolbar button:hover::before {
  width: 100%;
  height: 100%;
}

/* Tag animations */
.blog-editor__tag {
  transition: all var(--transition-normal) var(--ease-custom);
  position: relative;
  overflow: hidden;
}

.blog-editor__tag::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.blog-editor__tag:hover::before {
  left: 100%;
}

/* Pulse animation for auto-save */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.blog-editor__auto-save .icon {
  animation: pulse 1s infinite;
}

/* Typing indicator animation */
@keyframes typing {
  0%,
  50% {
    border-right-color: var(--color-primary);
  }
  51%,
  100% {
    border-right-color: transparent;
  }
}

.blog-editor__textarea:focus {
  animation: typing 1s infinite;
  border-right: 2px solid var(--color-primary);
}

/* Preview content fade-in animation */
.blog-editor__preview-content > * {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth scrollbar animations */
.blog-editor__preview-content::-webkit-scrollbar-thumb {
  transition: background-color var(--transition-normal) var(--ease-custom);
}

/* Button ripple effect */
.blog-editor__save,
.blog-editor__toggle,
.blog-editor__export,
.blog-editor__import-btn,
.blog-editor__copy,
.blog-editor__fullscreen {
  position: relative;
  overflow: hidden;
}

.blog-editor__save::after,
.blog-editor__toggle::after,
.blog-editor__export::after,
.blog-editor__import-btn::after,
.blog-editor__copy::after,
.blog-editor__fullscreen::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.blog-editor__save:active::after,
.blog-editor__toggle:active::after,
.blog-editor__export:active::after,
.blog-editor__import-btn:active::after,
.blog-editor__copy:active::after,
.blog-editor__fullscreen:active::after {
  width: 200%;
  height: 200%;
}

/* Icon styles */
.icon {
  width: 1em;
  height: 1em;
  flex-shrink: 0;
}

/* Dark theme specific adjustments */
.theme-dark .blog-editor__toolbar button {
  background: var(--color-gray-700);
  border-color: var(--color-gray-600);
  color: var(--color-gray-300);
}

.theme-dark .blog-editor__toolbar button:hover {
  background: var(--color-primary);
  color: var(--color-white);
}

.theme-dark .blog-editor__tag {
  background: linear-gradient(135deg, var(--color-primary-800), var(--color-primary-900));
  color: var(--color-primary-200);
  border-color: var(--color-primary-700);
}

.theme-dark .blog-editor__tag-remove {
  color: var(--color-primary-300);
}

.theme-dark .blog-editor__tag-remove:hover {
  background: var(--color-primary-700);
}
/* D
ark theme background */
.theme-dark.blog-editor {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1600 900'%3E%3Cpolygon fill='%231a1a2e' points='957 450 539 900 1396 900'/%3E%3Cpolygon fill='%23252547' points='957 450 872.9 900 1396 900'/%3E%3Cpolygon fill='%2316213e' points='-60 900 398 662 816 900'/%3E%3Cpolygon fill='%23233463' points='337 900 398 662 816 900'/%3E%3Cpolygon fill='%230f3460' points='1203 546 1552 900 876 900'/%3E%3Cpolygon fill='%23533483' points='1203 546 1552 900 1162 900'/%3E%3Cpolygon fill='%237209b7' points='641 695 886 900 367 900'/%3E%3Cpolygon fill='%238b5cf6' points='587 900 641 695 886 900'/%3E%3Cpolygon fill='%23a855f7' points='1710 900 1401 632 1096 900'/%3E%3Cpolygon fill='%23c084fc' points='1710 900 1401 632 1365 900'/%3E%3Cpolygon fill='%23d8b4fe' points='1210 900 971 687 725 900'/%3E%3Cpolygon fill='%23e9d5ff' points='943 900 1210 900 971 687'/%3E%3C/svg%3E"),
    radial-gradient(circle at 20% 80%, rgba(79, 70, 229, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(147, 51, 234, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.2) 0%, transparent 50%);
  background-size: cover, 800px 800px, 600px 600px, 400px 400px;
  background-position: center, 0% 0%, 100% 100%, 50% 50%;
  background-attachment: fixed;
}

.theme-dark.blog-editor::before {
  background: linear-gradient(
      45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.01) 50%,
      transparent 70%
    ),
    linear-gradient(-45deg, transparent 30%, rgba(255, 255, 255, 0.01) 50%, transparent 70%);
}
/* Action buttons grouping for better organization */
.blog-editor__actions-primary {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.blog-editor__actions-secondary {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

/* Compact button variant for when space is limited */
.blog-editor__actions.compact .blog-editor__toggle,
.blog-editor__actions.compact .blog-editor__save,
.blog-editor__actions.compact .blog-editor__export,
.blog-editor__actions.compact .blog-editor__fullscreen,
.blog-editor__actions.compact .blog-editor__copy,
.blog-editor__actions.compact .blog-editor__import-btn {
  padding: 0.5rem 0.75rem;
  font-size: var(--font-size-xs);
  gap: 0.25rem;
}

/* Hide button text on very small buttons, show only icons */
.blog-editor__actions.compact .blog-editor__toggle span:not(.icon),
.blog-editor__actions.compact .blog-editor__save span:not(.icon),
.blog-editor__actions.compact .blog-editor__export span:not(.icon),
.blog-editor__actions.compact .blog-editor__fullscreen span:not(.icon),
.blog-editor__actions.compact .blog-editor__copy span:not(.icon),
.blog-editor__actions.compact .blog-editor__import-btn span:not(.icon) {
  display: none;
}

@media (max-width: 900px) {
  .blog-editor__actions {
    max-width: 300px;
  }
}

@media (max-width: 600px) {
  .blog-editor__actions {
    max-width: 250px;
  }
}/* En
hanced tooltips styling */

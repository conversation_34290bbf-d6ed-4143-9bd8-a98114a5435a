.blogs-page {
  @apply min-h-screen bg-gray-50 dark:bg-gray-900;
}

/* Hero Section */
.blogs-page__hero {
  @apply relative py-20 lg:py-32 bg-gradient-to-br from-blue-50 to-indigo-100 
         dark:from-gray-900 dark:to-blue-900/20 overflow-hidden;
}

.hero-content {
  @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10;
}

.hero-badge {
  @apply inline-flex items-center gap-2 px-4 py-2 bg-blue-100 
         dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 
         rounded-full text-sm font-medium mb-8;
}

.badge-icon {
  @apply w-4 h-4;
}

.hero-title {
  @apply text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  @apply text-xl lg:text-2xl text-gray-600 dark:text-gray-300 mb-10 
         max-w-3xl mx-auto leading-relaxed;
}

.hero-actions {
  @apply flex flex-col sm:flex-row items-center justify-center gap-4;
}

.action-btn {
  @apply inline-flex items-center gap-2 px-6 py-3 font-medium rounded-xl 
         transition-all duration-300 hover:scale-105 hover:shadow-lg;
}

.action-btn--primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white;
}

.action-btn--secondary {
  @apply bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 
         text-gray-700 dark:text-gray-300 border border-gray-300 
         dark:border-gray-600;
}

/* Hero Decoration */
.hero-decoration {
  @apply absolute inset-0 pointer-events-none;
}

.decoration-shape {
  @apply absolute rounded-full opacity-10;
}

.decoration-shape--1 {
  @apply w-96 h-96 bg-blue-500 -top-48 -right-48;
  animation: float 6s ease-in-out infinite;
}

.decoration-shape--2 {
  @apply w-64 h-64 bg-purple-500 top-1/2 -left-32;
  animation: float 8s ease-in-out infinite reverse;
}

.decoration-shape--3 {
  @apply w-32 h-32 bg-pink-500 bottom-20 right-20;
  animation: float 4s ease-in-out infinite;
}

/* Content Section */
.blogs-page__content {
  @apply py-16;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .blogs-page__hero {
    @apply py-16;
  }
  
  .hero-title {
    @apply text-4xl;
  }
  
  .hero-subtitle {
    @apply text-lg;
  }
  
  .hero-actions {
    @apply flex-col;
  }
  
  .action-btn {
    @apply w-full justify-center;
  }
  
  .blogs-page__content {
    @apply py-12;
  }
}

/* Dark mode specific styles */
@media (prefers-color-scheme: dark) {
  .blogs-page__hero {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  }
  
  .hero-title {
    background: linear-gradient(135deg, #90cdf4 0%, #a78bfa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* Focus states for accessibility */
.action-btn:focus {
  @apply ring-2 ring-blue-500 ring-offset-2 ring-offset-white 
         dark:ring-offset-gray-900;
}

/* Print styles */
@media print {
  .hero-decoration {
    display: none;
  }
  
  .blogs-page__hero {
    @apply bg-white text-black py-8;
  }
  
  .hero-actions {
    display: none;
  }
}

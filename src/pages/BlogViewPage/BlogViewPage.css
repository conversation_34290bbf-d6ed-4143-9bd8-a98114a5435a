.blog-view-page {
  @apply min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300;
}

.blog-view-page.fullscreen {
  @apply fixed inset-0 z-50 overflow-auto;
}

/* Header */
.blog-view-page__header {
  @apply sticky top-0 z-40 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md 
         border-b border-gray-200 dark:border-gray-700;
}

.header-content {
  @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between;
}

.back-link {
  @apply inline-flex items-center gap-2 text-gray-600 dark:text-gray-300 
         hover:text-gray-900 dark:hover:text-white transition-colors;
}

.header-actions {
  @apply flex items-center gap-3;
}

.action-btn {
  @apply inline-flex items-center gap-2 px-3 py-2 text-sm font-medium 
         text-gray-600 dark:text-gray-300 hover:text-gray-900 
         dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 
         rounded-lg transition-all duration-200;
}

.action-btn.active {
  @apply text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20;
}

/* Article */
.blog-view-page__article {
  @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12;
}

/* Hero */
.article-hero {
  @apply mb-12;
}

.article-thumbnail {
  @apply aspect-video rounded-2xl overflow-hidden mb-8 shadow-lg;
}

.article-thumbnail img {
  @apply w-full h-full object-cover;
}

.article-header {
  @apply text-center;
}

.article-category {
  @apply inline-block px-3 py-1 text-sm font-semibold bg-blue-100 
         dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 
         rounded-full mb-4;
}

.article-title {
  @apply text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white 
         mb-6 leading-tight;
}

.article-excerpt {
  @apply text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed 
         max-w-3xl mx-auto;
}

.article-meta {
  @apply flex flex-wrap items-center justify-center gap-6 mb-8 
         text-gray-500 dark:text-gray-400;
}

.meta-item {
  @apply flex items-center gap-2;
}

.meta-icon {
  @apply w-4 h-4;
}

.article-tags {
  @apply flex flex-wrap items-center justify-center gap-2;
}

.tag {
  @apply inline-flex items-center gap-1 px-3 py-1 text-sm bg-gray-100 
         dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full;
}

/* Content */
.article-content {
  @apply prose prose-lg dark:prose-invert max-w-none mb-12;
  
  /* Custom prose styles */
  --tw-prose-body: theme('colors.gray.700');
  --tw-prose-headings: theme('colors.gray.900');
  --tw-prose-lead: theme('colors.gray.600');
  --tw-prose-links: theme('colors.blue.600');
  --tw-prose-bold: theme('colors.gray.900');
  --tw-prose-counters: theme('colors.gray.500');
  --tw-prose-bullets: theme('colors.gray.300');
  --tw-prose-hr: theme('colors.gray.200');
  --tw-prose-quotes: theme('colors.gray.900');
  --tw-prose-quote-borders: theme('colors.gray.200');
  --tw-prose-captions: theme('colors.gray.500');
  --tw-prose-code: theme('colors.gray.900');
  --tw-prose-pre-code: theme('colors.gray.200');
  --tw-prose-pre-bg: theme('colors.gray.800');
  --tw-prose-th-borders: theme('colors.gray.300');
  --tw-prose-td-borders: theme('colors.gray.200');
}

.dark .article-content {
  --tw-prose-body: theme('colors.gray.300');
  --tw-prose-headings: theme('colors.white');
  --tw-prose-lead: theme('colors.gray.400');
  --tw-prose-links: theme('colors.blue.400');
  --tw-prose-bold: theme('colors.white');
  --tw-prose-counters: theme('colors.gray.400');
  --tw-prose-bullets: theme('colors.gray.600');
  --tw-prose-hr: theme('colors.gray.700');
  --tw-prose-quotes: theme('colors.gray.100');
  --tw-prose-quote-borders: theme('colors.gray.700');
  --tw-prose-captions: theme('colors.gray.400');
  --tw-prose-code: theme('colors.white');
  --tw-prose-pre-code: theme('colors.gray.300');
  --tw-prose-pre-bg: theme('colors.gray.800');
  --tw-prose-th-borders: theme('colors.gray.600');
  --tw-prose-td-borders: theme('colors.gray.700');
}

/* Article Actions */
.article-actions {
  @apply flex flex-wrap items-center justify-center gap-4 py-8 
         border-t border-b border-gray-200 dark:border-gray-700 mb-12;
}

.share-container {
  @apply relative;
}

.share-menu {
  @apply absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 
         bg-white dark:bg-gray-800 rounded-lg shadow-lg border 
         border-gray-200 dark:border-gray-700 py-2 min-w-[120px];
}

.share-menu button {
  @apply w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 
         hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors;
}

.share-overlay {
  @apply fixed inset-0 z-30;
}

/* Related Posts */
.blog-view-page__related {
  @apply max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12 
         border-t border-gray-200 dark:border-gray-700;
}

.blog-view-page__related h2 {
  @apply text-3xl font-bold text-gray-900 dark:text-white text-center mb-12;
}

.related-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8;
}

/* Navigation */
.blog-view-page__navigation {
  @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center;
}

.nav-link {
  @apply inline-flex items-center gap-2 px-6 py-3 bg-blue-600 
         hover:bg-blue-700 text-white font-medium rounded-xl 
         transition-all duration-300 hover:scale-105 hover:shadow-lg;
}

/* Loading and Error States */
.blog-view-page__loading,
.blog-view-page__error {
  @apply flex flex-col items-center justify-center min-h-screen 
         text-gray-500 dark:text-gray-400;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full mb-4;
}

.blog-view-page__error h1 {
  @apply text-3xl font-bold text-gray-900 dark:text-white mb-4;
}

.blog-view-page__error p {
  @apply text-lg mb-8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .article-title {
    @apply text-3xl;
  }
  
  .article-excerpt {
    @apply text-lg;
  }
  
  .article-meta {
    @apply flex-col gap-3;
  }
  
  .article-actions {
    @apply flex-col;
  }
  
  .header-actions {
    @apply gap-2;
  }
  
  .action-btn span {
    @apply hidden;
  }
}

/* Print Styles */
@media print {
  .blog-view-page__header,
  .article-actions,
  .blog-view-page__related,
  .blog-view-page__navigation {
    display: none;
  }
  
  .blog-view-page {
    @apply bg-white text-black;
  }
  
  .article-content {
    @apply text-black;
  }
}

/* Focus states for accessibility */
.action-btn:focus,
.back-link:focus,
.nav-link:focus {
  @apply ring-2 ring-blue-500 ring-offset-2 ring-offset-white 
         dark:ring-offset-gray-900;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
.blog-view-page::-webkit-scrollbar {
  width: 8px;
}

.blog-view-page::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

.blog-view-page::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

.blog-view-page::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

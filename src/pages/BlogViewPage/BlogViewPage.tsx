import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useParams, Link, useNavigate } from 'react-router-dom';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow, prism } from 'react-syntax-highlighter/dist/esm/styles/prism';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import {
  FiArrowLeft,
  FiCalendar,
  FiUser,
  FiClock,
  FiTag,
  FiShare2,
  FiEdit,
  FiBookmark,
  FiHeart,
  FiMessageCircle,
  FiSun,
  FiMoon,
  FiMaximize2,
  FiMinimize2
} from 'react-icons/fi';
import PageAnimation from '../../components/common/PageAnimation/PageAnimation';
import { BlogCard, BlogNavigation, ShareButton } from '../../components/blog';
import { useBlogPost } from '../../hooks/useBlogPost';
import { useBlogList } from '../../hooks/useBlogList';
import { generateBlogUrl } from '../../utils/blogUtils';
import './BlogViewPage.css';

const BlogViewPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  // const navigate = useNavigate();
  
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isLiked, setIsLiked] = useState(false);

  // Find blog by slug
  const { blogs } = useBlogList();
  const currentBlog = blogs.find(blog => blog.slug === slug);
  
  const {
    blog,
    isLoading,
    error,
    loadBlog
  } = useBlogPost({ blogId: currentBlog?.id });

  // Load blog when component mounts
  useEffect(() => {
    if (currentBlog?.id) {
      loadBlog(currentBlog.id);
    }
  }, [currentBlog?.id, loadBlog]);

  // Get related blogs
  const relatedBlogs = blogs
    .filter(b =>
      b.id !== currentBlog?.id &&
      b.status === 'published' &&
      (b.category === currentBlog?.category ||
       b.tags.some(tag => currentBlog?.tags.includes(tag)))
    )
    .slice(0, 3);

  // Get previous and next blogs for navigation
  const publishedBlogs = blogs
    .filter(b => b.status === 'published')
    .sort((a, b) => new Date(b.publishedAt || b.createdAt).getTime() - new Date(a.publishedAt || a.createdAt).getTime());

  const currentIndex = publishedBlogs.findIndex(b => b.id === currentBlog?.id);
  const previousBlog = currentIndex > 0 ? publishedBlogs[currentIndex - 1] : undefined;
  const nextBlog = currentIndex < publishedBlogs.length - 1 ? publishedBlogs[currentIndex + 1] : undefined;

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };



  if (isLoading) {
    return (
      <PageAnimation type="fade-in" duration={0.8} className="blog-view-page">
        <div className="blog-view-page__loading">
          <motion.div
            className="loading-spinner"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
          <span>Loading blog post...</span>
        </div>
      </PageAnimation>
    );
  }

  if (error || !blog) {
    return (
      <PageAnimation type="fade-in" duration={0.8} className="blog-view-page">
        <div className="blog-view-page__error">
          <h1>Blog Post Not Found</h1>
          <p>The blog post you're looking for doesn't exist or has been removed.</p>
          <Link to="/blogs" className="back-link">
            <FiArrowLeft />
            Back to Blog
          </Link>
        </div>
      </PageAnimation>
    );
  }

  return (
    <PageAnimation 
      type="fade-in" 
      duration={0.8} 
      className={`blog-view-page ${isDarkMode ? 'dark-mode' : ''} ${isFullscreen ? 'fullscreen' : ''}`}
    >
      {/* Header */}
      <motion.header
        className="blog-view-page__header"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="header-content">
          <Link to="/blogs" className="back-link">
            <FiArrowLeft />
            <span>Back to Blog</span>
          </Link>

          <div className="header-actions">
            <button
              onClick={() => setIsDarkMode(!isDarkMode)}
              className="action-btn"
              title="Toggle dark mode"
            >
              {isDarkMode ? <FiSun /> : <FiMoon />}
            </button>
            
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="action-btn"
              title="Toggle fullscreen"
            >
              {isFullscreen ? <FiMinimize2 /> : <FiMaximize2 />}
            </button>

            <Link to={`/blog-editor/${blog.id}`} className="action-btn">
              <FiEdit />
              <span>Edit</span>
            </Link>
          </div>
        </div>
      </motion.header>

      {/* Article */}
      <motion.article
        className="blog-view-page__article"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        {/* Hero */}
        <div className="article-hero">
          {blog.thumbnail && (
            <div className="article-thumbnail">
              <img src={blog.thumbnail} alt={blog.title} />
            </div>
          )}
          
          <div className="article-header">
            {blog.category && (
              <span className="article-category">{blog.category}</span>
            )}
            
            <h1 className="article-title">{blog.title}</h1>
            
            <p className="article-excerpt">{blog.excerpt}</p>
            
            <div className="article-meta">
              <div className="meta-item">
                <FiUser className="meta-icon" />
                <span>{blog.author}</span>
              </div>
              <div className="meta-item">
                <FiCalendar className="meta-icon" />
                <span>{formatDate(blog.publishedAt || blog.createdAt)}</span>
              </div>
              <div className="meta-item">
                <FiClock className="meta-icon" />
                <span>{blog.readingTime} min read</span>
              </div>
            </div>

            {blog.tags.length > 0 && (
              <div className="article-tags">
                {blog.tags.map(tag => (
                  <span key={tag} className="tag">
                    <FiTag />
                    {tag}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="article-content">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeRaw]}
            components={{
              code(props) {
                const { children, className } = props;
                const match = /language-(\w+)/.exec(className || "");
                return match ? (
                  <SyntaxHighlighter
                    style={isDarkMode ? tomorrow : prism}
                    language={match[1]}
                    PreTag="div"
                  >
                    {String(children).replace(/\n$/, "")}
                  </SyntaxHighlighter>
                ) : (
                  <code className={className}>{children}</code>
                );
              },
            }}
          >
            {blog.content}
          </ReactMarkdown>
        </div>

        {/* Actions */}
        <div className="article-actions">
          <button
            onClick={() => setIsLiked(!isLiked)}
            className={`action-btn ${isLiked ? 'active' : ''}`}
          >
            <FiHeart />
            <span>Like</span>
          </button>

          <button
            onClick={() => setIsBookmarked(!isBookmarked)}
            className={`action-btn ${isBookmarked ? 'active' : ''}`}
          >
            <FiBookmark />
            <span>Bookmark</span>
          </button>

          <ShareButton
            title={blog.title}
            url={generateBlogUrl(blog.slug)}
            excerpt={blog.excerpt}
            hashtags={blog.tags}
            variant="button"
            showLabels={true}
          />

          <button className="action-btn">
            <FiMessageCircle />
            <span>Comment</span>
          </button>
        </div>
      </motion.article>

      {/* Related Posts */}
      {relatedBlogs.length > 0 && (
        <motion.section
          className="blog-view-page__related"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <h2>Related Posts</h2>
          <div className="related-grid">
            {relatedBlogs.map((relatedBlog, index) => (
              <motion.div
                key={relatedBlog.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.1 * index }}
              >
                <BlogCard
                  blog={relatedBlog}
                  variant="compact"
                  showActions={true}
                  showThumbnail={true}
                  showExcerpt={true}
                  showMeta={true}
                  showTags={false}
                />
              </motion.div>
            ))}
          </div>
        </motion.section>
      )}

      {/* Navigation */}
      {currentBlog && (
        <BlogNavigation
          currentBlog={currentBlog}
          previousBlog={previousBlog}
          nextBlog={nextBlog}
          allBlogsLink="/blogs"
        />
      )}


    </PageAnimation>
  );
};

export default BlogViewPage;
